[{"_id": "674a1b2c3d4e5f6789012401", "user": "674a1b2c3d4e5f6789012346", "serviceAreas": ["Bangalore", "Mumbai", "Pune"], "specialties": ["Residential Construction", "Interior Design", "Renovation"], "portfolio": [{"image": "https://example.com/portfolio/priya1.jpg", "caption": "Modern 3BHK apartment construction in Whitefield, Bangalore", "createdAt": "2024-01-16T12:00:00.000Z"}, {"image": "https://example.com/portfolio/priya2.jpg", "caption": "Luxury villa interior design in Bandra, Mumbai", "createdAt": "2024-01-20T14:30:00.000Z"}], "experience": 8, "ratings": 4.7, "verificationStatus": "verified", "verifiedBy": "674a1b2c3d4e5f6789012501", "approvalDate": "2024-01-18T10:00:00.000Z", "reasonForRejection": "", "createdAt": "2024-01-16T11:45:00.000Z", "updatedAt": "2024-01-18T10:00:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012402", "user": "674a1b2c3d4e5f6789012349", "serviceAreas": ["Delhi", "Gurgaon", "Noida"], "specialties": ["Commercial Construction", "High-rise Buildings", "Infrastructure"], "portfolio": [{"image": "https://example.com/portfolio/vikram1.jpg", "caption": "15-story commercial complex in Connaught Place, Delhi", "createdAt": "2024-01-19T16:45:00.000Z"}], "experience": 12, "ratings": 4.9, "verificationStatus": "verified", "verifiedBy": "674a1b2c3d4e5f6789012501", "approvalDate": "2024-01-21T09:30:00.000Z", "reasonForRejection": "", "createdAt": "2024-01-19T16:30:00.000Z", "updatedAt": "2024-01-21T09:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012403", "user": "674a1b2c3d4e5f678901234c", "serviceAreas": ["Chennai", "Coimbatore", "Madurai"], "specialties": ["Traditional Architecture", "Temple Construction", "Heritage Restoration"], "portfolio": [{"image": "https://example.com/portfolio/kavya1.jpg", "caption": "Traditional Tamil Nadu style house in T. Nagar, Chennai", "createdAt": "2024-01-22T16:00:00.000Z"}, {"image": "https://example.com/portfolio/kavya2.jpg", "caption": "Heritage temple restoration project in Madurai", "createdAt": "2024-01-25T11:15:00.000Z"}], "experience": 6, "ratings": 4.5, "verificationStatus": "pending", "verifiedBy": null, "approvalDate": null, "reasonForRejection": "", "createdAt": "2024-01-22T15:30:00.000Z", "updatedAt": "2024-01-22T15:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012404", "user": "674a1b2c3d4e5f6789012350", "serviceAreas": ["Indore", "Bhopal", "<PERSON><PERSON><PERSON><PERSON>"], "specialties": ["Eco-friendly Construction", "Solar Installation", "Green Buildings"], "portfolio": [{"image": "https://example.com/portfolio/anita1.jpg", "caption": "Solar-powered eco-friendly home in Indore", "createdAt": "2024-01-26T12:00:00.000Z"}], "experience": 5, "ratings": 4.3, "verificationStatus": "verified", "verifiedBy": "674a1b2c3d4e5f6789012502", "approvalDate": "2024-01-28T14:20:00.000Z", "reasonForRejection": "", "createdAt": "2024-01-26T11:20:00.000Z", "updatedAt": "2024-01-28T14:20:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012405", "user": "674a1b2c3d4e5f6789012351", "serviceAreas": ["<PERSON><PERSON>", "Thiruvananthapuram", "Kozhikode"], "specialties": ["Waterproofing", "Coastal Construction", "Monsoon-resistant Buildings"], "portfolio": [], "experience": 10, "ratings": 4.6, "verificationStatus": "rejected", "verifiedBy": "674a1b2c3d4e5f6789012501", "approvalDate": null, "reasonForRejection": "Incomplete documentation provided", "createdAt": "2024-01-27T10:30:00.000Z", "updatedAt": "2024-01-29T16:45:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012406", "user": "674a1b2c3d4e5f6789012352", "serviceAreas": ["Ahmedabad", "Surat", "Vadodara"], "specialties": ["Industrial Construction", "Factory Setup", "Warehouse Construction"], "portfolio": [{"image": "https://example.com/portfolio/contractor6_1.jpg", "caption": "Large textile factory construction in Surat", "createdAt": "2024-01-28T09:15:00.000Z"}], "experience": 15, "ratings": 4.8, "verificationStatus": "verified", "verifiedBy": "674a1b2c3d4e5f6789012502", "approvalDate": "2024-01-30T11:00:00.000Z", "reasonForRejection": "", "createdAt": "2024-01-28T08:45:00.000Z", "updatedAt": "2024-01-30T11:00:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012407", "user": "674a1b2c3d4e5f6789012353", "serviceAreas": ["Jaipur", "Jodhpur", "Udaipur"], "specialties": ["Rajasthani Architecture", "Palace Restoration", "Desert Construction"], "portfolio": [{"image": "https://example.com/portfolio/contractor7_1.jpg", "caption": "Traditional Rajasthani haveli construction in Jaipur", "createdAt": "2024-01-29T13:30:00.000Z"}], "experience": 9, "ratings": 4.4, "verificationStatus": "pending", "verifiedBy": null, "approvalDate": null, "reasonForRejection": "", "createdAt": "2024-01-29T12:15:00.000Z", "updatedAt": "2024-01-29T12:15:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012408", "user": "674a1b2c3d4e5f6789012354", "serviceAreas": ["Kolkata", "Durgapur", "Siliguri"], "specialties": ["Bridge Construction", "Road Construction", "Urban Planning"], "portfolio": [{"image": "https://example.com/portfolio/contractor8_1.jpg", "caption": "Modern flyover construction in Kolkata", "createdAt": "2024-01-30T10:45:00.000Z"}], "experience": 11, "ratings": 4.7, "verificationStatus": "verified", "verifiedBy": "674a1b2c3d4e5f6789012501", "approvalDate": "2024-02-01T15:30:00.000Z", "reasonForRejection": "", "createdAt": "2024-01-30T09:20:00.000Z", "updatedAt": "2024-02-01T15:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012409", "user": "674a1b2c3d4e5f6789012355", "serviceAreas": ["Hyderabad", "Warangal", "Nizamabad"], "specialties": ["IT Park Construction", "Tech Infrastructure", "Smart Buildings"], "portfolio": [{"image": "https://example.com/portfolio/contractor9_1.jpg", "caption": "State-of-the-art IT campus in HITEC City, Hyderabad", "createdAt": "2024-02-01T14:20:00.000Z"}], "experience": 7, "ratings": 4.6, "verificationStatus": "verified", "verifiedBy": "674a1b2c3d4e5f6789012502", "approvalDate": "2024-02-03T12:15:00.000Z", "reasonForRejection": "", "createdAt": "2024-02-01T13:45:00.000Z", "updatedAt": "2024-02-03T12:15:00.000Z"}, {"_id": "674a1b2c3d4e5f678901240a", "user": "674a1b2c3d4e5f6789012356", "serviceAreas": ["Lucknow", "Kanpur", "<PERSON><PERSON><PERSON>"], "specialties": ["Government Projects", "Public Infrastructure", "Educational Buildings"], "portfolio": [{"image": "https://example.com/portfolio/contractor10_1.jpg", "caption": "Modern university campus construction in Lucknow", "createdAt": "2024-02-02T11:30:00.000Z"}], "experience": 13, "ratings": 4.5, "verificationStatus": "pending", "verifiedBy": null, "approvalDate": null, "reasonForRejection": "", "createdAt": "2024-02-02T10:00:00.000Z", "updatedAt": "2024-02-02T10:00:00.000Z"}]
[{"_id": "674a1b2c3d4e5f6789012801", "userId": "674a1b2c3d4e5f6789012345", "projectName": "Green Valley Residential Construction", "contractorId": "674a1b2c3d4e5f6789012401", "brokerId": "674a1b2c3d4e5f6789012305", "status": "In Progress", "actualStartDate": "2024-02-01T00:00:00.000Z", "actualEndDate": null, "progressLogs": [{"date": "2024-02-01T09:00:00.000Z", "stage": "Foundation", "description": "Foundation work started, excavation completed", "addedById": "674a1b2c3d4e5f6789012401", "addedByRole": "Contractor"}, {"date": "2024-02-15T10:30:00.000Z", "stage": "Foundation", "description": "Foundation concrete work completed", "addedById": "674a1b2c3d4e5f6789012401", "addedByRole": "Contractor"}, {"date": "2024-03-01T11:15:00.000Z", "stage": "Structure", "description": "Ground floor structure work in progress", "addedById": "674a1b2c3d4e5f6789012401", "addedByRole": "Contractor"}], "remarks": "Project progressing as per schedule", "createdAt": "2024-01-28T14:30:00.000Z", "updatedAt": "2024-03-01T11:15:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012802", "userId": "674a1b2c3d4e5f6789012348", "projectName": "Sunrise Commercial Complex", "contractorId": "674a1b2c3d4e5f6789012409", "brokerId": "674a1b2c3d4e5f6789012307", "status": "Planning", "actualStartDate": null, "actualEndDate": null, "progressLogs": [{"date": "2024-02-05T14:00:00.000Z", "stage": "Planning", "description": "Architectural drawings approved by client", "addedById": "674a1b2c3d4e5f6789012409", "addedByRole": "Contractor"}, {"date": "2024-02-10T16:30:00.000Z", "stage": "Planning", "description": "Structural engineering plans submitted for approval", "addedById": "674a1b2c3d4e5f6789012409", "addedByRole": "Contractor"}], "remarks": "Waiting for municipal approvals", "createdAt": "2024-02-03T10:15:00.000Z", "updatedAt": "2024-02-10T16:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012803", "userId": "674a1b2c3d4e5f678901234f", "projectName": "Tech Park Development", "contractorId": "674a1b2c3d4e5f6789012402", "brokerId": "674a1b2c3d4e5f6789012305", "status": "Completed", "actualStartDate": "2023-08-15T00:00:00.000Z", "actualEndDate": "2024-01-20T00:00:00.000Z", "progressLogs": [{"date": "2023-08-15T08:00:00.000Z", "stage": "Foundation", "description": "Project kickoff and site preparation", "addedById": "674a1b2c3d4e5f6789012402", "addedByRole": "Contractor"}, {"date": "2023-10-01T09:30:00.000Z", "stage": "Structure", "description": "Main structure construction completed", "addedById": "674a1b2c3d4e5f6789012402", "addedByRole": "Contractor"}, {"date": "2023-12-15T14:45:00.000Z", "stage": "Finishing", "description": "Interior work and finishing in progress", "addedById": "674a1b2c3d4e5f6789012402", "addedByRole": "Contractor"}, {"date": "2024-01-20T16:00:00.000Z", "stage": "Handover", "description": "Project completed and handed over to client", "addedById": "674a1b2c3d4e5f6789012402", "addedByRole": "Contractor"}], "remarks": "Successfully completed within timeline and budget", "createdAt": "2023-08-10T12:00:00.000Z", "updatedAt": "2024-01-20T16:00:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012804", "userId": "674a1b2c3d4e5f678901234c", "projectName": "Hillside Resort Construction", "contractorId": "674a1b2c3d4e5f6789012403", "brokerId": "674a1b2c3d4e5f6789012304", "status": "On Hold", "actualStartDate": "2024-02-15T00:00:00.000Z", "actualEndDate": null, "progressLogs": [{"date": "2024-02-15T10:00:00.000Z", "stage": "Site Preparation", "description": "Site clearing and leveling started", "addedById": "674a1b2c3d4e5f6789012403", "addedByRole": "Contractor"}, {"date": "2024-02-28T15:30:00.000Z", "stage": "Site Preparation", "description": "Work halted due to environmental clearance issues", "addedById": "674a1b2c3d4e5f6789012304", "addedByRole": "Broker"}], "remarks": "Project on hold pending environmental clearance", "createdAt": "2024-02-10T11:30:00.000Z", "updatedAt": "2024-02-28T15:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012805", "userId": "674a1b2c3d4e5f6789012349", "projectName": "Metro Plaza Commercial Development", "contractorId": "674a1b2c3d4e5f6789012402", "brokerId": "674a1b2c3d4e5f6789012306", "status": "Initiated", "actualStartDate": null, "actualEndDate": null, "progressLogs": [{"date": "2024-02-20T12:00:00.000Z", "stage": "Initial Planning", "description": "Project requirements gathering completed", "addedById": "674a1b2c3d4e5f6789012306", "addedByRole": "Broker"}], "remarks": "Initial planning phase", "createdAt": "2024-02-18T09:45:00.000Z", "updatedAt": "2024-02-20T12:00:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012806", "userId": "674a1b2c3d4e5f678901234b", "projectName": "Ocean View Villa Construction", "contractorId": "674a1b2c3d4e5f6789012405", "brokerId": "674a1b2c3d4e5f6789012309", "status": "Cancelled", "actualStartDate": null, "actualEndDate": null, "progressLogs": [{"date": "2024-02-12T14:30:00.000Z", "stage": "Planning", "description": "Initial site survey completed", "addedById": "674a1b2c3d4e5f6789012405", "addedByRole": "Contractor"}, {"date": "2024-02-25T10:15:00.000Z", "stage": "Cancelled", "description": "Project cancelled due to budget constraints", "addedById": "674a1b2c3d4e5f6789012309", "addedByRole": "Broker"}], "remarks": "Project cancelled by client due to budget issues", "createdAt": "2024-02-08T16:20:00.000Z", "updatedAt": "2024-02-25T10:15:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012807", "userId": "674a1b2c3d4e5f678901234d", "projectName": "Heritage City Residential Villa", "contractorId": "674a1b2c3d4e5f6789012407", "brokerId": "674a1b2c3d4e5f6789012308", "status": "Planning", "actualStartDate": null, "actualEndDate": null, "progressLogs": [{"date": "2024-03-01T11:00:00.000Z", "stage": "Design", "description": "Traditional Rajasthani architecture design finalized", "addedById": "674a1b2c3d4e5f6789012407", "addedByRole": "Contractor"}], "remarks": "Focusing on traditional Rajasthani architectural elements", "createdAt": "2024-02-28T13:45:00.000Z", "updatedAt": "2024-03-01T11:00:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012808", "userId": "674a1b2c3d4e5f678901234a", "projectName": "Lakefront Luxury Villa", "contractorId": "674a1b2c3d4e5f6789012401", "brokerId": "674a1b2c3d4e5f6789012302", "status": "In Progress", "actualStartDate": "2024-03-05T00:00:00.000Z", "actualEndDate": null, "progressLogs": [{"date": "2024-03-05T08:30:00.000Z", "stage": "Foundation", "description": "Foundation excavation work started", "addedById": "674a1b2c3d4e5f6789012401", "addedByRole": "Contractor"}], "remarks": "Premium lakefront villa project", "createdAt": "2024-03-02T10:30:00.000Z", "updatedAt": "2024-03-05T08:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789012809", "userId": "674a1b2c3d4e5f6789012350", "projectName": "Eco-Friendly Smart Home", "contractorId": "674a1b2c3d4e5f6789012404", "brokerId": "674a1b2c3d4e5f678901230a", "status": "In Progress", "actualStartDate": "2024-02-20T00:00:00.000Z", "actualEndDate": null, "progressLogs": [{"date": "2024-02-20T09:00:00.000Z", "stage": "Foundation", "description": "Eco-friendly foundation materials installation", "addedById": "674a1b2c3d4e5f6789012404", "addedByRole": "Contractor"}, {"date": "2024-03-01T14:20:00.000Z", "stage": "Solar Installation", "description": "Solar panel framework installation completed", "addedById": "674a1b2c3d4e5f6789012404", "addedByRole": "Contractor"}], "remarks": "Focus on sustainable and eco-friendly construction", "createdAt": "2024-02-15T12:15:00.000Z", "updatedAt": "2024-03-01T14:20:00.000Z"}, {"_id": "674a1b2c3d4e5f678901280a", "userId": "674a1b2c3d4e5f6789012347", "projectName": "Industrial Warehouse Complex", "contractorId": "674a1b2c3d4e5f6789012406", "brokerId": "674a1b2c3d4e5f6789012301", "status": "Planning", "actualStartDate": null, "actualEndDate": null, "progressLogs": [{"date": "2024-03-03T10:45:00.000Z", "stage": "Planning", "description": "Industrial design specifications finalized", "addedById": "674a1b2c3d4e5f6789012406", "addedByRole": "Contractor"}], "remarks": "Large-scale industrial warehouse project", "createdAt": "2024-03-01T15:30:00.000Z", "updatedAt": "2024-03-03T10:45:00.000Z"}]
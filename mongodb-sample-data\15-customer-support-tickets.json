[{"_id": "674a1b2c3d4e5f6789013701", "user_id": "674a1b2c3d4e5f6789012345", "ticketId": "TICKET-001", "subject": "Payment Gateway Issue", "description": "Unable to complete payment for site verification. Getting error message 'Transaction Failed'.", "category": "payment", "priority": "high", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012501", "resolution": "Payment gateway issue was resolved. User was able to complete the payment successfully.", "createdAt": "2024-01-15T11:30:00.000Z", "updatedAt": "2024-01-15T14:20:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013702", "user_id": "674a1b2c3d4e5f6789012347", "ticketId": "TICKET-002", "subject": "Profile Verification Delay", "description": "My broker profile verification is taking longer than expected. It's been 5 days since submission.", "category": "verification", "priority": "medium", "status": "in_progress", "assignedTo": "674a1b2c3d4e5f6789012502", "resolution": "", "createdAt": "2024-01-18T10:15:00.000Z", "updatedAt": "2024-01-19T09:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013703", "user_id": "674a1b2c3d4e5f6789012348", "ticketId": "TICKET-003", "subject": "Project Communication Issues", "description": "Contractor is not responding to messages. Need help in establishing communication for project updates.", "category": "communication", "priority": "high", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012503", "resolution": "Facilitated communication between client and contractor. Issue resolved through direct call.", "createdAt": "2024-02-05T14:45:00.000Z", "updatedAt": "2024-02-06T11:20:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013704", "user_id": "674a1b2c3d4e5f678901234c", "ticketId": "TICKET-004", "subject": "Refund Request", "description": "Project got cancelled due to environmental clearance issues. Requesting refund of advance payment.", "category": "refund", "priority": "high", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012504", "resolution": "Refund of ₹25,000 processed successfully. Amount credited to user's account.", "createdAt": "2024-02-28T16:30:00.000Z", "updatedAt": "2024-03-01T10:15:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013705", "user_id": "674a1b2c3d4e5f678901234d", "ticketId": "TICKET-005", "subject": "Site Verification Rejection Appeal", "description": "My site verification was rejected citing incomplete documents. I believe all documents were provided correctly.", "category": "verification", "priority": "medium", "status": "open", "assignedTo": "674a1b2c3d4e5f6789012501", "resolution": "", "createdAt": "2024-01-26T09:45:00.000Z", "updatedAt": "2024-01-26T09:45:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013706", "user_id": "674a1b2c3d4e5f6789012349", "ticketId": "TICKET-006", "subject": "Commission Payment Delay", "description": "My contractor commission payment is delayed by 2 weeks. Need assistance in tracking the payment.", "category": "payment", "priority": "medium", "status": "in_progress", "assignedTo": "674a1b2c3d4e5f6789012505", "resolution": "", "createdAt": "2024-03-04T13:20:00.000Z", "updatedAt": "2024-03-05T08:45:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013707", "user_id": "674a1b2c3d4e5f678901234a", "ticketId": "TICKET-007", "subject": "Site Listing Not Visible", "description": "My site listing 'Lakefront Villa Development' is not appearing in search results despite being approved.", "category": "technical", "priority": "medium", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012506", "resolution": "Search index was updated. Site listing is now visible in search results.", "createdAt": "2024-02-01T11:30:00.000Z", "updatedAt": "2024-02-01T16:45:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013708", "user_id": "674a1b2c3d4e5f678901234e", "ticketId": "TICKET-008", "subject": "Account Access Issues", "description": "Unable to login to my account. Password reset is not working. Getting 'Invalid credentials' error.", "category": "account", "priority": "high", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012507", "resolution": "Account access restored. Password reset link was sent to registered email address.", "createdAt": "2024-01-30T09:15:00.000Z", "updatedAt": "2024-01-30T12:30:00.000Z"}, {"_id": "674a1b2c3d4e5f6789013709", "user_id": "674a1b2c3d4e5f6789012350", "ticketId": "TICKET-009", "subject": "Portfolio Upload Problem", "description": "Unable to upload portfolio images. Getting 'File size too large' error even for small images.", "category": "technical", "priority": "low", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012508", "resolution": "File upload limit was increased. User can now upload portfolio images successfully.", "createdAt": "2024-01-27T15:20:00.000Z", "updatedAt": "2024-01-28T10:45:00.000Z"}, {"_id": "674a1b2c3d4e5f678901370a", "user_id": "674a1b2c3d4e5f678901234f", "ticketId": "TICKET-010", "subject": "Project Completion Certificate", "description": "Need project completion certificate for 'Tech Park Development' project for tax purposes.", "category": "documentation", "priority": "low", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012509", "resolution": "Project completion certificate generated and sent to user's registered email address.", "createdAt": "2024-01-22T14:30:00.000Z", "updatedAt": "2024-01-23T09:15:00.000Z"}, {"_id": "674a1b2c3d4e5f678901370b", "user_id": "674a1b2c3d4e5f678901234b", "ticketId": "TICKET-011", "subject": "Contractor Recommendation", "description": "Looking for contractor recommendations for coastal construction in Kerala. Need expert advice.", "category": "consultation", "priority": "low", "status": "open", "assignedTo": "674a1b2c3d4e5f678901250a", "resolution": "", "createdAt": "2024-02-15T12:45:00.000Z", "updatedAt": "2024-02-15T12:45:00.000Z"}, {"_id": "674a1b2c3d4e5f678901370c", "user_id": "674a1b2c3d4e5f6789012346", "ticketId": "TICKET-012", "subject": "Rating System Clarification", "description": "Need clarification on how the rating system works and how it affects my contractor profile visibility.", "category": "general", "priority": "low", "status": "resolved", "assignedTo": "674a1b2c3d4e5f6789012501", "resolution": "Detailed explanation of rating system provided via email. User queries resolved.", "createdAt": "2024-03-02T11:15:00.000Z", "updatedAt": "2024-03-02T14:30:00.000Z"}]
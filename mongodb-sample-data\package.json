{"name": "build-connect-test-data", "version": "1.0.0", "description": "Test data insertion script for Build Connect MongoDB database", "main": "insert-test-data.js", "scripts": {"setup": "node insert-test-data.js && node validate-data.js", "install-data": "node insert-test-data.js", "generate-hash": "node generate-hash.js", "validate": "node validate-data.js", "clear-db": "node clear-database.js", "test": "node insert-test-data.js && node validate-data.js"}, "keywords": ["mongodb", "test-data", "build-connect", "sample-data"], "author": "Build Connect Team", "license": "MIT", "dependencies": {"mongodb": "^6.3.0", "bcrypt": "^5.1.1"}, "engines": {"node": ">=14.0.0"}}
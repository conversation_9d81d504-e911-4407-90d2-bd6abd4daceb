#!/usr/bin/env node

/**
 * Build Connect - Data Validation Script
 * 
 * This script validates the inserted data and checks relationships
 * 
 * Usage: node validate-data.js
 */

const { MongoClient } = require('mongodb');
const bcrypt = require('bcrypt');

// Configuration
const config = {
    mongoUrl: process.env.MONGODB_URL || 'mongodb://localhost:27017',
    dbName: process.env.DB_NAME || 'build_connect_db',
    testPassword: 'Build@123'
};

class DataValidator {
    constructor() {
        this.client = null;
        this.db = null;
    }

    async connect() {
        try {
            console.log('🔌 Connecting to MongoDB...');
            this.client = new MongoClient(config.mongoUrl);
            await this.client.connect();
            this.db = this.client.db(config.dbName);
            console.log(`✅ Connected to database: ${config.dbName}`);
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            throw error;
        }
    }

    async validateCollectionCounts() {
        const expectedCounts = {
            users: 12,
            contractors: 10,
            brokers: 12,
            sites: 10,
            projects: 10,
            admins: 10,
            verificationrequests: 15,
            servicerequests: 12,
            assets: 15,
            aadhaars: 12,
            pans: 12,
            transactions: 12,
            notifications: 12,
            ratings: 12,
            customers: 12,
            siteassets: 15,
            encumbrancecertificates: 10,
            propertytaxreceipts: 10
        };

        console.log('\n📊 Validating collection counts...\n');
        
        let totalExpected = 0;
        let totalActual = 0;
        const results = [];

        for (const [collectionName, expectedCount] of Object.entries(expectedCounts)) {
            try {
                const actualCount = await this.db.collection(collectionName).countDocuments();
                const status = actualCount === expectedCount ? '✅' : '❌';
                
                console.log(`${status} ${collectionName.padEnd(25)} ${actualCount.toString().padStart(3)}/${expectedCount}`);
                
                totalExpected += expectedCount;
                totalActual += actualCount;
                
                results.push({
                    collection: collectionName,
                    expected: expectedCount,
                    actual: actualCount,
                    valid: actualCount === expectedCount
                });
                
            } catch (error) {
                console.log(`❌ ${collectionName.padEnd(25)} ERROR: ${error.message}`);
                results.push({
                    collection: collectionName,
                    expected: expectedCount,
                    actual: 0,
                    valid: false,
                    error: error.message
                });
            }
        }

        console.log('\n' + '='.repeat(40));
        console.log(`Total Expected: ${totalExpected}`);
        console.log(`Total Actual: ${totalActual}`);
        console.log(`Match: ${totalActual === totalExpected ? '✅' : '❌'}`);
        
        return results;
    }

    async validatePasswords() {
        console.log('\n🔐 Validating passwords...\n');
        
        try {
            // Test user passwords
            const users = await this.db.collection('users').find({}).limit(3).toArray();
            let validPasswords = 0;
            
            for (const user of users) {
                const isValid = await bcrypt.compare(config.testPassword, user.password);
                const status = isValid ? '✅' : '❌';
                console.log(`${status} ${user.email.padEnd(30)} Password: ${isValid ? 'Valid' : 'Invalid'}`);
                if (isValid) validPasswords++;
            }
            
            // Test admin passwords
            const admins = await this.db.collection('admins').find({}).limit(2).toArray();
            
            for (const admin of admins) {
                const isValid = await bcrypt.compare(config.testPassword, admin.password);
                const status = isValid ? '✅' : '❌';
                console.log(`${status} ${admin.email.padEnd(30)} Password: ${isValid ? 'Valid' : 'Invalid'}`);
                if (isValid) validPasswords++;
            }
            
            console.log(`\n✅ Valid passwords: ${validPasswords}/${users.length + admins.length}`);
            return validPasswords === (users.length + admins.length);
            
        } catch (error) {
            console.error('❌ Password validation failed:', error.message);
            return false;
        }
    }

    async validateRelationships() {
        console.log('\n🔗 Validating data relationships...\n');
        
        const validations = [];
        
        try {
            // Check if contractors reference valid users
            const contractors = await this.db.collection('contractors').find({}).toArray();
            const contractorUserIds = contractors.map(c => c.user);
            const contractorUsers = await this.db.collection('users').find({
                _id: { $in: contractorUserIds }
            }).toArray();
            
            const contractorValid = contractorUsers.length === contractors.length;
            console.log(`✅ Contractor-User relationships: ${contractorUsers.length}/${contractors.length}`);
            validations.push(contractorValid);
            
            // Check if brokers reference valid users
            const brokers = await this.db.collection('brokers').find({}).toArray();
            const brokerUserIds = brokers.map(b => b.user);
            const brokerUsers = await this.db.collection('users').find({
                _id: { $in: brokerUserIds }
            }).toArray();
            
            const brokerValid = brokerUsers.length === brokers.length;
            console.log(`✅ Broker-User relationships: ${brokerUsers.length}/${brokers.length}`);
            validations.push(brokerValid);
            
            // Check if sites have valid owners
            const sites = await this.db.collection('sites').find({}).toArray();
            const siteUserIds = sites.map(s => s.userId);
            const siteUsers = await this.db.collection('users').find({
                _id: { $in: siteUserIds }
            }).toArray();
            
            const siteValid = siteUsers.length === sites.length;
            console.log(`✅ Site-User relationships: ${siteUsers.length}/${sites.length}`);
            validations.push(siteValid);
            
            // Check projects have valid users
            const projects = await this.db.collection('projects').find({}).toArray();
            const projectUserIds = projects.map(p => p.userId);
            const projectUsers = await this.db.collection('users').find({
                _id: { $in: projectUserIds }
            }).toArray();
            
            const projectValid = projectUsers.length === projects.length;
            console.log(`✅ Project-User relationships: ${projectUsers.length}/${projects.length}`);
            validations.push(projectValid);
            
            return validations.every(v => v);
            
        } catch (error) {
            console.error('❌ Relationship validation failed:', error.message);
            return false;
        }
    }

    async validateIndexes() {
        console.log('\n📊 Validating database indexes...\n');
        
        try {
            const collections = ['users', 'sites', 'contractors', 'brokers', 'aadhaars', 'pans'];
            let indexCount = 0;
            
            for (const collectionName of collections) {
                const indexes = await this.db.collection(collectionName).indexes();
                console.log(`✅ ${collectionName.padEnd(20)} ${indexes.length} indexes`);
                indexCount += indexes.length;
            }
            
            console.log(`\n✅ Total indexes created: ${indexCount}`);
            return indexCount > collections.length; // Should have more than just _id indexes
            
        } catch (error) {
            console.error('❌ Index validation failed:', error.message);
            return false;
        }
    }

    async runFullValidation() {
        console.log('\n' + '='.repeat(60));
        console.log('🔍 BUILD CONNECT - DATA VALIDATION REPORT');
        console.log('='.repeat(60));
        
        const results = {
            counts: await this.validateCollectionCounts(),
            passwords: await this.validatePasswords(),
            relationships: await this.validateRelationships(),
            indexes: await this.validateIndexes()
        };
        
        console.log('\n' + '='.repeat(60));
        console.log('📋 VALIDATION SUMMARY');
        console.log('='.repeat(60));
        
        const countValid = results.counts.every(r => r.valid);
        console.log(`📊 Collection Counts: ${countValid ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🔐 Password Hashing: ${results.passwords ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`🔗 Data Relationships: ${results.relationships ? '✅ PASS' : '❌ FAIL'}`);
        console.log(`📊 Database Indexes: ${results.indexes ? '✅ PASS' : '❌ FAIL'}`);
        
        const overallValid = countValid && results.passwords && results.relationships && results.indexes;
        console.log('\n' + '='.repeat(60));
        console.log(`🎯 OVERALL STATUS: ${overallValid ? '✅ ALL VALIDATIONS PASSED' : '❌ SOME VALIDATIONS FAILED'}`);
        console.log('='.repeat(60));
        
        if (overallValid) {
            console.log('\n🎉 Your Build Connect database is ready for development!');
            console.log('🔐 Login with any user email and password: Build@123');
        }
        
        return overallValid;
    }

    async close() {
        if (this.client) {
            await this.client.close();
            console.log('\n🔌 Database connection closed');
        }
    }
}

// Main execution function
async function main() {
    const validator = new DataValidator();
    
    try {
        await validator.connect();
        const isValid = await validator.runFullValidation();
        
        process.exit(isValid ? 0 : 1);
        
    } catch (error) {
        console.error('💥 Validation failed:', error.message);
        process.exit(1);
    } finally {
        await validator.close();
    }
}

// Run the script
if (require.main === module) {
    main();
}

module.exports = { DataValidator, config };
